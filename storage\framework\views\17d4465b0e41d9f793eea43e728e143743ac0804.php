
<?php echo $__env->make('inc.success', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('inc.style', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php echo $__env->make('inc.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->startSection('content'); ?>
<?php echo $__env->make('inc.title', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<br><br>
<div class="container">
    <h3>Task Details</h3>
    <p><strong>Task Title:</strong> <?php echo e($task->title); ?></p>
    <p><strong>Description:</strong> <?php echo e($task->description); ?></p>
    <p><strong>End Date:</strong> <?php echo e(\Carbon\Carbon::parse($task->end_date)->format('Y-m-d')); ?></p>
    <?php if($isSprintOverdue): ?>
        <div class="alert alert-warning">The sprint is overdue!</div>
    <?php else: ?>
        <?php if($isTaskOverdue): ?>
            <div class="alert alert-danger">This task is overdue!</div>
        <?php endif; ?>
    <?php endif; ?>

    <hr>
    <h3>Comments
        <?php if($commentCount > 0): ?>
            <small class="text-muted">(<?php echo e($commentCount); ?> total)</small>
        <?php endif; ?>
    </h3>

        <form method="GET" action="<?php echo e(route('tasks.viewCommentList', ['task_id' => $task->id, 'sprint_id' => $task->sprint_id])); ?>">
            <div class="form-group">
                <label for="created_by">Filter by Created By</label>
                <select name="created_by" id="created_by" class="form-control" onchange="this.form.submit()">
                    <option value="">All</option>
                    <?php $__currentLoopData = $uniqueCreators; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $creator): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($creator); ?>" <?php echo e(request('created_by') == $creator ? 'selected' : ''); ?>>
                            <?php echo e($creator); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>

            <div class="form-group">
                <label for="sort_date">Sort by Date</label>
                <select name="sort_date" id="sort_date" class="form-control" onchange="this.form.submit()">
                    <option value="desc" <?php echo e(request('sort_date') == 'desc' ? 'selected' : ''); ?>>Newest</option>
                    <option value="asc" <?php echo e(request('sort_date') == 'asc' ? 'selected' : ''); ?>>Oldest</option>
                </select>
            </div>
        </form>

    <hr>
    <?php if(!$hasComments || $comments->isEmpty()): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> No comments available for this task.
            <?php if($commentCount > 0): ?>
                <small class="text-muted">(<?php echo e($commentCount); ?> total comments, but none match current filters)</small>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Comment</th>
                    <th>Created By</th>
                    <th>Updated At</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td><?php echo e($comment->comment); ?></td>
                        <td><?php echo e($comment->created_by); ?></td>
                        <td><?php echo e(\Carbon\Carbon::parse($comment->updated_at)->format('Y-m-d')); ?></td>
                        <td>
                            <?php if(!$isTaskOverdue && !$isSprintOverdue && $comment->created_by === auth()->user()->username): ?>
                                <a href="<?php echo e(route('tasks.editComment', $comment->id)); ?>" class="btn btn-warning btn-sm">Edit</a>
                                <form action="<?php echo e(route('tasks.deleteComment', $comment->id)); ?>" method="POST" style="display:inline;">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure want to delete this comment?')">Delete</button>
                                </form>
                            <?php else: ?>
                                <span class="text-muted">No Actions Available</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    <?php endif; ?>

    <div class="pagination-container">
        <?php echo e($comments->appends(request()->query())->links('pagination::bootstrap-4')); ?>

    </div>

    <?php if(!$isTaskOverdue && !$isSprintOverdue): ?>
        <a href="<?php echo e(route('tasks.createComment', ['task_id' => $task->id, $task->sprint_id])); ?>" class="btn btn-success">Add Comment</a>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/tasks/viewCommentList.blade.php ENDPATH**/ ?>