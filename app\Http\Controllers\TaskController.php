<?php

namespace App\Http\Controllers;

use App\Task;
use App\Team;
use App\TeamMapping;
use App\Status;
use App\UserStory;
use App\User;
use App\Sprint;
use App\Project;
use App\Http\Controllers\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\taskComment;
use Illuminate\Support\Facades\Mail;
use App\Mail\CommentCreated;
class TaskController extends Controller
{
    //Main Sprint Page
    public function index($userstory_id)
    {
        //Get the project where user's team name(s) is the same with project's team name
        $user = \Auth::user();
        $teammapping = \App\TeamMapping::where('username', '=', $user->username)->pluck('team_name')->toArray(); // use pluck() to retrieve an array of team names
        $pro = \App\Project::whereIn('team_name', $teammapping)->get(); // use whereIn() to retrieve the projects that have a team_name value in the array

        //Get the task that is related to the userstory 
        $tasks = Task::where('userstory_id', $userstory_id)->get();

        //Get the userstory that is passed in the parameter
        $userstory = UserStory::where('u_id', $userstory_id)->first();
        $statuses = Status::all();

        return view('tasks.index')
            ->with('userstory_id', $userstory_id)
            ->with('tasks', $tasks)
            ->with('statuses', $statuses)
            ->with('title', 'Tasks for ' . $userstory->user_story)
            ->with('pros', $pro);
    }

    public function indexCalendar($userstory_id){

        //Get the project where user's team name(s) is the same with project's team name
        $user = \Auth::user();
        $teammapping = \App\TeamMapping::where('username', '=', $user->username)->pluck('team_name')->toArray(); // use pluck() to retrieve an array of team names
        $pro = \App\Project::whereIn('team_name', $teammapping)->get(); // use whereIn() to retrieve the projects that have a team_name value in the array

        //Get the task that is related to the userstory 
        $tasks = Task::where('userstory_id', $userstory_id)->get();
        //Get the userstory that is passed in the parameter
        $userstory = UserStory::where('u_id', $userstory_id)->first();

        return view('tasks.calendarTask');
        // ->with('userstory_id', $userstory_id)
        // ->with('tasks', $tasks)
        // ->with('title', 'Tasks for ' . $userstory->user_story)
        // ->with('pros', $pro);
    }

    //index Kanban Board
    public function indexKanbanBoard() 
    {
        //the function will send the required data to the kanban board to display
        //the kanban board will display all tasks that is related to the user's team's project

        $user = \Auth::user();
        $teammapping = \App\TeamMapping::where('username', '=', $user->username)->pluck('team_name')->toArray(); // use pluck() to retrieve an array of team names
        $pro = \App\Project::whereIn('team_name', $teammapping)->get(); // use whereIn() to retrieve the projects that have a team_name value in the array
        
        return view('tasks.kanban')
            ->with('pro', $pro);
    }

    //view specific Kanban Board
    public function kanbanIndex($proj_id, $sprint_id)
    {
        $sprint = Sprint::getSprintById($sprint_id);
        $project = Project::getProjectById($proj_id);
        $statuses = Status::getStatusesByProjectId($proj_id);
        $tasks = Task::getTasksByProjectAndSprintID($proj_id, $sprint_id);

        // Get the sort_date parameter from the request (default to 'desc' if not set)
        $sortDate = request('sort_date', 'desc');
        $isSprintOverdue =\Carbon\Carbon::now()->greaterThan($sprint->end_sprint);

        foreach ($tasks as $task) {
            // Check if the task is overdue
            $task->isOverdue = $task->end_date && \Carbon\Carbon::now()->greaterThan($task->end_date);

            // Fetch and sort comments for the task
            $query = taskComment::where('task_id', $task->id);
            $task->comments = taskComment::sortByDate($query, $sortDate)->get();
        }

        // Group tasks by status id
        $tasksByStatus = [];
        foreach ($tasks as $task) {
            $tasksByStatus[$task->status_id][] = $task;
        }

        return view('kanban.index', [
            'statuses' => $statuses,
            'tasksByStatus' => $tasksByStatus,
            'sprint' => $sprint,
            'project' => $project,
            'sort_date' => $sortDate,
            'isSprintOverdue' => $isSprintOverdue,
        ]);
    }

    public function updateKanbanBoard(Request $request, $id) {
        $task = Task::find($id);
      
        // Check if the task exists
        if (!$task) {
          return response()->json(['message' => 'Task not found'], 404);
        }
      
        // Update the task with the new status_name
        $task->status_name = $request->input('status_name');
        $task->save();
      
        return response()->json(['message' => 'Task updated successfully']);
      }

    public function getTaskDescription($task_id)
    {
        $task = Task::find($task_id);

        if (!$task) {
            // Task not found, handle accordingly
            return response()->json(['message' => 'Task not found'], 404);
        }

        return response()->json(['description' => $task->description]);
    }
      

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($userstory_id)
    {
        $userstory = UserStory::where('u_id', $userstory_id)->first();

        //get the project and sprint related to the task 
        $sprint = Sprint::where('sprint_id', $userstory->sprint_id)->first();
        $project = Project::where('proj_name', $sprint->proj_name)->first();

        //get the team for the project //tukar get kalau nak ambik semua
        $team = Project::where('proj_name', $project->proj_name)->get();

        //get the list of team members for the team //for each team
        //$teamlist = TeamMapping::where('team_name', $team->team_name)->get();
        $userTeams = [];

        // Iterate through each team
        foreach ($team as $teamItem) {
            // Get the list of team members for the current team
            $teamlist = TeamMapping::where('team_name', $teamItem->team_name)->get();

            // Now $teamlist contains the team members for the current team
            foreach ($teamlist as $teammember) {
                // Access individual team member properties like $teammember->username
                // Do something with each team member

                // Save username and team_name in a 2D array
                $userTeams[] = [
                    'username' => $teammember->username,
                    'team_name' => $teamItem->team_name,
                ];
            }
        }


        // Get the proj_name from the project
        $team_name = $project->team_name;
        
        //send the existing statuses for the project related   
        $status = Status::where('project_id', $project->id)->get();

        return view('tasks.create')
        ->with('title', 'Create Task for '. $userstory->user_story)
        ->with('statuses', $status)
        ->with('teamlist',  $userTeams)
        ->with('sprint', $sprint)
        ->with('userstory_id', $userstory_id);
    }

    // Redirect to Create Task Page
    public function createTask(Request $request)
    {
        $sprintId = $request->input('sprintId');
        $statusId = $request->input('statusId');
        $sprint = Sprint::where('sprint_id', $sprintId)->first();
        $sprintProjName = $sprint->proj_name;
        $sprintProj = Project::where('proj_name', $sprintProjName)->first();
        $sprintProjId = $sprintProj->id;
 
        $userStories = UserStory::where('sprint_id', $sprintId)->get();
        $userList = User::all();

        //get the team for the project //tukar get kalau nak ambik semua
        $team = Project::where('proj_name', $sprintProjName)->get();
        // Iterate through each team
        foreach ($team as $teamItem) {
            // Get the list of team members for the current team
            $teamlist = TeamMapping::where('team_name', $teamItem->team_name)->get();

            // Now $teamlist contains the team members for the current team
            foreach ($teamlist as $teammember) {
                // Access individual team member properties like $teammember->username
                // Do something with each team member

                // Save username and team_name in a 2D array
                $userTeams[] = [
                    'username' => $teammember->username,
                    'team_name' => $teamItem->team_name,
                ];
            }
        }
        

        return view('kanban.addTask', [
            'userStories' => $userStories,
            'userList' => $userList,
            'sprint_id' => $sprintId,
            'status_id' => $statusId,
            'sprintProjId' => $sprintProjId,
            'sprint' => $sprint,
            'teamlist' =>  $userTeams
        ]);
        
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

     public function viewCommentList(Request $request, $task_id, $sprint)
     {
        // Ensure user is authenticated
        if (!\Auth::check()) {
        return redirect()->route('login')->with('error', 'You must be logged in to access this page.');
        }
        // Get current user
        //$user = \Auth::user();
         // Fetch the task
        $task = Task::getTaskById($task_id);

         // Check if the task exists
        if (!$task) {
            return redirect()->back()->with('error', 'Task not found.');
        }
        $sprint = Sprint::getSprintById($task->sprint_id);
        if (!$sprint) {
            return redirect()->back()->with('error', 'Sprint not found.');
        }
        $isSprintOverdue =\Carbon\Carbon::now()->greaterThan($sprint->end_sprint);
        $isTaskOverdue = \Carbon\Carbon::now()->greaterThan($task->end_date);

        // Get unique creators for filter dropdown
        $uniqueCreators = TaskComment::getCreatorsByTask($task_id);

        // Get filter and sort parameters from request
        $createdBy = $request->get('created_by');
        $sortDate = $request->get('sort_date', 'desc');

        // Only apply filter if selected creator is valid
        $validCreator = in_array($createdBy, $uniqueCreators) ? $createdBy : null;

        // Use the user-defined function to get filtered and sorted comments with pagination
        $comments = TaskComment::getTaskCommentsWithFilters($task_id, $validCreator, $sortDate, 5);

        // Validate that comments were retrieved successfully
        if ($comments === null) {
            return redirect()->back()->with('error', 'Comment not found.');
        }

        // Check if task has any comments for additional context
        //$hasComments = TaskComment::hasComments($task_id);
    
         // Get the user's projects
        //  $teammapping = TeamMapping::where('username', $user->username)->pluck('team_name')->toArray();
        //  $pro = Project::whereIn('team_name', $teammapping)->get();
     
         // Pass the task and its comments to the view
         return view('tasks.viewCommentList', [
             'title' => 'Comments for ' . $task->title,
             'task' => $task,
             'comments' => $comments,
             'uniqueCreators' => $uniqueCreators,
             'isTaskOverdue' => $isTaskOverdue,
             'isSprintOverdue' => $isSprintOverdue,
             //'hasComments' => $hasComments,
         ]);
     }


public function createComment($task_id,$sprints)
{
    // Fetch the task to ensure it exists
    $task = Task::getTaskById($task_id);

    // If the task does not exist, redirect back with an error
    if (!$task) {
        return redirect()->back()->with('error', 'Task not found.');
    }

    $sprints = Sprint::getSprintById($task->sprint_id);
    if (!$sprints) {
        // Handle the case where no sprint is found
        return redirect()->back()->with('error', 'Sprint not found for this task.');
    }

    // Fetch the current authenticated user
    //$user = \Auth::user();

    // Get the user's related projects and team members
    //$project = Project::getProjectById($task->proj_id);
    //$teamNames = TeamMapping::getTeamNamesByProject($project->proj_name);
    //$members = TeamMapping::getTeamMembersByProject($project->proj_name);
    //$pro = Project::getProjectByTeamName($teamNames);
    
    $isTaskOverdue = \Carbon\Carbon::now()->greaterThan($task->end_date);
    $isSprintOverdue =\Carbon\Carbon::now()->greaterThan($sprints->end_sprint);

    // Pass data to the view
    return view('tasks.createComment', [
        'title' => 'Add Comment to ' . $task->title,
        'task' => $task,
        //'user' => $user,
        //'members' => $members,
        //'pros' => $pro,
        'sprints' => $sprints,
        'isTaskOverdue' => $isTaskOverdue,
        'isSprintOverdue' => $isSprintOverdue,
    ]);
}

public function storeComment(Request $request, $task_id)
{
    $request->validate([
        'comment' => 'required|string|max:1000',
    ]);

    $task = Task::getTaskById($task_id);
    if (!$task) {
        return redirect()->back()->with('error', 'Task not found.');
    }

    $comment = new TaskComment();
    $comment->task_id = $task_id;
    $comment->comment = $request->comment;
    $comment->created_by = \Auth::user()->username;
    $comment->assigned_to = json_encode(json_decode($task->user_names, true));
    $comment->created_at = now();
    $comment->save();

    // Fetch project and sprint data
    $project = Project::getProjectById($task->proj_id);
    $sprint = Sprint::getSprintById($task->sprint_id);

    // Send Gmail notifications
    \App\Helpers\NotificationHelper::sendGmailNotifications($comment, $task, $project, $sprint, 'created');

    return redirect()->route('tasks.viewCommentList', ['task_id' => $task->id, 'sprint_id' => $task->sprint_id])
        ->with('success', 'Comment added successfully and emails sent.');
}

public function editComment($comment_id)
{
    // Fetch the comment to ensure it exists
    $comment = TaskComment::getCommentById($comment_id);

    if (!$comment) {
        return redirect()->back()->with('error', 'Comment not found.');
    }

    // Fetch related task for context
    $task = Task::getTaskById($comment->task_id);
    if (!$task) {
        return redirect()->back()->with('error', 'Task not found.');
    }

    $sprint = Sprint::getSprintById($task->sprint_id);
    if (!$sprint) {
        return redirect()->back()->with('error', 'Sprint not found.');
    }

    $isTaskOverdue = \Carbon\Carbon::now()->greaterThan($task->end_date);
    $isSprintOverdue =\Carbon\Carbon::now()->greaterThan($sprint->end_sprint);
    // Pass data to the edit view
    return view('tasks.editComment', [
        'title' => 'Edit Comment',
        'comment' => $comment,
        'task' => $task,
        'sprint' => $sprint,
        'isTaskOverdue' => $isTaskOverdue,
        'isSprintOverdue' => $isSprintOverdue,
    ]);
}
public function createKanbanComment(Request $request, $task_id)
{
    $request->validate([
        'comment' => 'required|string|max:255',
    ]);

    $task = Task::getTaskById($task_id);
    if (!$task) {
        return response()->json(['success' => false, 'message' => 'Task not found'], 404);
    }

    $comment = new TaskComment();
    $comment->task_id = $task_id;
    $comment->comment = $request->comment;
    $comment->created_by = \Auth::user()->username;
    $comment->assigned_to = json_encode(json_decode($task->user_names, true));
    $comment->created_at = now();
    $comment->save();

    // Fetch project and sprint data
    $project = Project::getProjectById($task->proj_id);
    $sprint = Sprint::getSprintById($task->sprint_id);

    // Send Gmail notifications
    \App\Helpers\NotificationHelper::sendGmailNotifications($comment, $task, $project, $sprint, 'created');

    return response()->json(['success' => true, 'message' => 'Comment created successfully and emails sent.']);
}

public function updateKanbanComment(Request $request, $comment_id)
{
    $comment = TaskComment::getCommentById($comment_id);

    if (!$comment) {
        return response()->json(['success' => false, 'message' => 'Comment not found'], 404);
    }

    $request->validate([
        'comment' => 'required|string|max:255',
    ]);

    // Update the comment
    $comment->comment = $request->input('comment');
    $comment->updated_at = now();
    $comment->save();

    // Send Gmail notifications
    //$task = Task::getTaskById($comment->task_id);
    //$project = Project::find($task->proj_id);
    //$sprint = Sprint::find($task->sprint_id);
    
    return response()->json(['success' => true, 'message' => 'Comment updated successfully']);
}


public function updateComment(Request $request, $comment_id)
{
    $request->validate([
        'comment' => 'required|string|max:1000',
    ]);

    $comment = TaskComment::getCommentById($comment_id);
    if (!$comment) {
        return redirect()->back()->with('error', 'Comment not found.');
    }

    $task = Task::getTaskById($comment->task_id);
    if (!$task) {
        return redirect()->back()->with('error', 'Task not found.');
    }

    $comment->comment = $request->comment;
    $comment->save();

    // Send gmail notifications
    //$project = Project::find($task->proj_id);
    //$sprint = Sprint::find($task->sprint_id);


    return redirect()->route('tasks.viewCommentList', ['task_id' => $task->id, 'sprint_id' => $task->sprint_id])
        ->with('success', 'Comment updated successfully.');
}


public function deleteComment($comment_id)
{
    // Fetch the comment to ensure it exists
    $comment = taskComment::getCommentById($comment_id);

    if (!$comment) {
        return redirect()->back()->with('error', 'Comment not found.');
    }

    // Send gmail notifications
    //$task = Task::find($comment->task_id);
    //$project = Project::find($task->proj_id);
    //$sprint = Sprint::find($task->sprint_id);

    // Delete the comment
    $comment->delete();

    // Redirect back with a success message
    return redirect()->back()->with('success', 'Comment deleted successfully.');
}




    public function store(Request $request)
    {
        $userstory = UserStory::where('u_id', $request->userstory_id)->first();

        //get the project and sprint related to the task 

        if ($request->isKanban == "1") {
            $sprint = Sprint::where('sprint_id', $request->sprint_id)->first();
        } else {
            $sprint = Sprint::where('sprint_id', $userstory->sprint_id)->first(); 
        }

        $project = Project::where('proj_name', $sprint->proj_name)->first();

        //validate the request
        $validation = $request->validate([
            //validate for existing task names
            'title' => 'required|unique:tasks,title,NULL,id,userstory_id,'.$request->userstory_id,
            'description' => 'required',

            //validate that start of task should be after or equal the sprint's start date
            'start_date' => 'required|date|after_or_equal:'.$sprint->start_sprint,

            //validate that end of task should be before or equal the sprint's end date
            'end_date' => 'required|date|before_or_equal:'.$sprint->end_sprint.'|after_or_equal:start_date',

        ], [
            'title.required' => '*The Task Name is required',
            'title.unique' => '*There is already an existing task in the userstory with the same name',
            'description.required' => '*The Description is required',
            'start_date.required' => '*The Start Date is required',
            'start_date.after_or_equal' => '*The Start Date must be equal to or after the sprint start date',
            'end_date.required' => '*The End Date is required',
            'end_date.before_or_equal' => '*The End Date must be equal to or before the sprint end date',
            'end_date.after_or_equal' => '*The End Date must be equal to or after the Start Date',
        ]);

        //assign request values to new task 
        $task = new Task();
        $task->userstory_id = $request->userstory_id;
        $task->title = $request->title;
        $task->description = $request->description;
        $task->user_names = json_encode($request->user_names);
        $task->status_id = $request->status_id;
        $task->start_date = $request->start_date;
        $task->end_date = $request->end_date;
        $task->proj_id = $project->id;
        $task->sprint_id = $sprint->sprint_id;
        $task->newTask_update = now()->timezone("Asia/Kuala_Lumpur")->toDateString();
        $task->save();

        $tasks = Task::where('userstory_id', $request->userstory_id)->get();
        //Get the project where user's team name(s) is the same with project's team name
        $user = \Auth::user();
        $teammapping = \App\TeamMapping::where('username', '=', $user->username)->pluck('team_name')->toArray(); // use pluck() to retrieve an array of team names
        $pro = \App\Project::whereIn('team_name', $teammapping)->get(); // use whereIn() to retrieve the projects that have a team_name value in the array
        $statuses = Status::all();

        if ($request->isKanban == "1") {
            return redirect()->route('sprint.kanbanPage', ['proj_id' => $request->sprintProjId, 'sprint_id' => $request->sprint_id]);
        } else {
            return redirect()->route('tasks.index', ['u_id' => $userstory->u_id])
            ->with('title', 'Tasks for ' . $userstory->user_story)
            ->with('success', 'Task has successfully been created!')
            ->with('task', $tasks)
            ->with('statuses', $statuses)
            ->with('userstory_id', $userstory->u_id)
            ->with('pros', $pro);
        }
    }

    public function sync(Request $request)
    {
        $this->validate(request(), [
            'columns' => ['required', 'array']
        ]);

        foreach ($request->columns as $status) {
            foreach ($status['tasks'] as $i => $task) {
                $order = $i + 1;
                if ($task['status_id'] !== $status['id'] || $task['order'] !== $order) {
                    request()->user()->tasks()
                        ->find($task['id'])
                        ->update(['status_id' => $status['id'], 'order' => $order]);
                }
            }
        }

        return $request->user()->statuses()->with('tasks')->get();
    }

    public function show(Task $task)
    {
        //
    }

    public function edit($task_id)
    {
        //Get the current task
        $task = Task::where('id', $task_id)->first();
        $userstory = UserStory::where('u_id', $task->userstory_id)->first();
        $sprint = Sprint::where('sprint_id', $task->sprint_id)->first();
        $project = Project::where('id', $task->proj_id)->first();

        //get the team for the project
        $team = Project::where('proj_name', $project->proj_name)->get();

        //get the list of team members for the team
        //$teamlist = TeamMapping::where('team_name', $team->team_name)->get();

        // Get the proj_name from the project
        //$team_name = $project->team_name;

        $userTeams = [];

        // Iterate through each team
        foreach ($team as $teamItem) {
            // Get the list of team members for the current team
            $teamlist = TeamMapping::where('team_name', $teamItem->team_name)->get();

            // Now $teamlist contains the team members for the current team
            foreach ($teamlist as $teammember) {
                // Access individual team member properties like $teammember->username
                // Do something with each team member

                // Save username and team_name in a 2D array
                $userTeams[] = [
                    'username' => $teammember->username,
                    'team_name' => $teamItem->team_name,
                ];
            }
        }

        
        //send the existing statuses for the project related   
        $status = Status::where('project_id', $project->id)->get();

        return view('tasks.edit')
            ->with('title', 'Edit '. $task->title . ' in '. $userstory->user_story)
            ->with('project', $project)
            ->with('sprint', $sprint)
            ->with('statuses', $status)
            ->with('teamlist', $userTeams)
            ->with('task', $task);
    }

    public function update(Request $request, Task $task)
    {
        //Get the current task
        $userstory = UserStory::where('u_id', $task->userstory_id)->first();
        $sprint = Sprint::where('sprint_id', $task->sprint_id)->first();
        $project = Project::where('id', $task->proj_id)->first();

        //validate the request
        $validation = $request->validate([
            //validate for existing task names
            'title' => 'required|unique:tasks,title,'.$task->id.',id,userstory_id,'.$userstory->u_id,
            'description' => 'required',

            //validate that start of task should be after or equal the sprint's start date
            'start_date' => 'required|date|after_or_equal:'.$sprint->start_sprint,

            //validate that end of task should be before or equal the sprint's end date
            'end_date' => 'required|date|before_or_equal:'.$sprint->end_sprint.'|after_or_equal:start_date',

        
        ], [
            'title.required' => '*The Task Name is required',
            'title.unique' => '*There is already an existing task in the userstory with the same name',
            'description.required' => '*The Description is required',
            'start_date.required' => '*The Start Date is required',
            'start_date.after_or_equal' => '*The Start Date must be equal to or after the sprint start date',
            'end_date.required' => '*The End Date is required',
            'end_date.before_or_equal' => '*The End Date must be equal to or before the sprint end date',
            'end_date.after_or_equal' => '*The End Date must be equal to or after the Start Date',
        ]);

        $task->title = $request->title;
        $task->description = $request->description;
        $task->user_names = json_encode($request->user_names);
        $task->status_id = $request->status_id;
        $task->start_date = $request->start_date;
        $task->end_date = $request->end_date;
        $task->newTask_update = now()->timezone("Asia/Kuala_Lumpur")->toDateString();
        $task->save();

        $tasks = Task::where('userstory_id', $task->userstory_id)->get();

        //Get the project where user's team name(s) is the same with project's team name
        $user = \Auth::user();
        $teammapping = \App\TeamMapping::where('username', '=', $user->username)->pluck('team_name')->toArray(); // use pluck() to retrieve an array of team names
        $pro = \App\Project::whereIn('team_name', $teammapping)->get(); // use whereIn() to retrieve the projects that have a team_name value in the array
        $statuses = Status::all();

        if ($request->isKanban == "1") {
            return redirect()->route('sprint.kanbanPage', ['proj_id' => $request->sprintProjId, 'sprint_id' => $request->sprint_id]);
        } else {
            return redirect()->route('tasks.index', ['u_id' => $userstory->u_id])
            ->with('title', 'Tasks for ' . $userstory->user_story)
            ->with('success', 'Task has successfully been updated!')
            ->with('task', $tasks)
            ->with('statuses', $statuses)
            ->with('userstory_id', $userstory->u_id)
            ->with('pros', $pro);
        }

    }

    // Redirect to updateTaskPage
    public function updateTaskPage($taskId)
    {
        $task = Task::findOrFail($taskId);
        $userList = User::all();
        $status_id = $task->status_id;
        $sprint_id = $task->sprint_id;
        $sprintProjId = $task->proj_id;  // Add this line
        $userStories = UserStory::where('sprint_id', $task->sprint_id)->get();
        $sprint = Sprint::where('sprint_id', $sprint_id)->first();
        $sprintProjName = $sprint->proj_name;

        //get the team for the project //tukar get kalau nak ambik semua
        $team = Project::where('proj_name', $sprintProjName)->get();
        // Iterate through each team
        foreach ($team as $teamItem) {
            // Get the list of team members for the current team
            $teamlist = TeamMapping::where('team_name', $teamItem->team_name)->get();

            // Now $teamlist contains the team members for the current team
            foreach ($teamlist as $teammember) {
                // Access individual team member properties like $teammember->username
                // Do something with each team member

                // Save username and team_name in a 2D array
                $userTeams[] = [
                    'username' => $teammember->username,
                    'team_name' => $teamItem->team_name,
                ];
            }
        }

        return view('kanban.updateTask', [
            'task' => $task,
            'userStories' => $userStories,
            'userList' => $userList,
            'status_id' => $status_id,
            'sprint_id' => $sprint_id,
            'sprintProjId' => $sprintProjId,  
            'sprint' => $sprint,
            'teamlist' =>  $userTeams
        ]);
    }

    public function destroy(Task $task)
    {
        $userstory = UserStory::where('u_id', $task->userstory_id)->first();
        $tasks = Task::where('userstory_id', $task->userstory_id)->get();

        //Get the project where user's team name(s) is the same with project's team name
        $user = \Auth::user();
        $teammapping = \App\TeamMapping::where('username', '=', $user->username)->pluck('team_name')->toArray(); // use pluck() to retrieve an array of team names
        $pro = \App\Project::whereIn('team_name', $teammapping)->get(); // use whereIn() to retrieve the projects that have a team_name value in the array
        $statuses = Status::all();


        $task->delete();

        return redirect()->route('tasks.index', ['u_id' => $userstory->u_id])
        ->with('title', 'Tasks for ' . $userstory->user_story)
        ->with('success', 'Task has successfully been deleted!')
        ->with('task', $tasks)
        ->with('statuses', $statuses)
        ->with('userstory_id', $userstory->u_id)
        ->with('pros', $pro);
    }

    // Delete a task
    public function deleteTask(Request $request)
    {
        $taskId = $request->input('taskId');

        try {
            // Find and delete the task
            Task::destroy($taskId);

            return response()->json(['success' => true, 'message' => 'Task deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => 'Error deleting task'], 500);
        }
    }


}