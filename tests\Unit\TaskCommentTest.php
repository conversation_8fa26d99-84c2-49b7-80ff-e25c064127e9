<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\TaskComment;
use App\Task;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class TaskCommentTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test the getTaskCommentsWithFilters method
     *
     * @return void
     */
    public function test_get_task_comments_with_filters()
    {
        // Create a task for testing
        $task = Task::factory()->create();
        
        // Create some test comments
        $comment1 = TaskComment::create([
            'task_id' => $task->id,
            'comment' => 'First comment',
            'created_by' => 'user1',
            'assigned_to' => '["user1"]',
            'created_at' => now()->subDays(2),
            'updated_at' => now()->subDays(2),
        ]);

        $comment2 = TaskComment::create([
            'task_id' => $task->id,
            'comment' => 'Second comment',
            'created_by' => 'user2',
            'assigned_to' => '["user2"]',
            'created_at' => now()->subDays(1),
            'updated_at' => now()->subDays(1),
        ]);

        $comment3 = TaskComment::create([
            'task_id' => $task->id,
            'comment' => 'Third comment',
            'created_by' => 'user1',
            'assigned_to' => '["user1"]',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Test getting all comments (no filter)
        $allComments = TaskComment::getTaskCommentsWithFilters($task->id);
        $this->assertEquals(3, $allComments->total());

        // Test filtering by creator
        $user1Comments = TaskComment::getTaskCommentsWithFilters($task->id, 'user1');
        $this->assertEquals(2, $user1Comments->total());

        $user2Comments = TaskComment::getTaskCommentsWithFilters($task->id, 'user2');
        $this->assertEquals(1, $user2Comments->total());

        // Test sorting (desc by default - newest first)
        $commentsDesc = TaskComment::getTaskCommentsWithFilters($task->id, null, 'desc');
        $this->assertEquals($comment3->id, $commentsDesc->first()->id);

        // Test sorting ascending (oldest first)
        $commentsAsc = TaskComment::getTaskCommentsWithFilters($task->id, null, 'asc');
        $this->assertEquals($comment1->id, $commentsAsc->first()->id);
    }

    /**
     * Test the hasComments method
     *
     * @return void
     */
    public function test_has_comments()
    {
        $task = Task::factory()->create();
        
        // Initially no comments
        $this->assertFalse(TaskComment::hasComments($task->id));

        // Add a comment
        TaskComment::create([
            'task_id' => $task->id,
            'comment' => 'Test comment',
            'created_by' => 'testuser',
            'assigned_to' => '["testuser"]',
        ]);

        // Now should have comments
        $this->assertTrue(TaskComment::hasComments($task->id));
    }

    /**
     * Test the getCommentCount method
     *
     * @return void
     */
    public function test_get_comment_count()
    {
        $task = Task::factory()->create();
        
        // Initially no comments
        $this->assertEquals(0, TaskComment::getCommentCount($task->id));

        // Add comments
        TaskComment::create([
            'task_id' => $task->id,
            'comment' => 'First comment',
            'created_by' => 'user1',
            'assigned_to' => '["user1"]',
        ]);

        TaskComment::create([
            'task_id' => $task->id,
            'comment' => 'Second comment',
            'created_by' => 'user2',
            'assigned_to' => '["user2"]',
        ]);

        // Should have 2 comments
        $this->assertEquals(2, TaskComment::getCommentCount($task->id));
    }

    /**
     * Test empty comments scenario
     *
     * @return void
     */
    public function test_empty_comments_scenario()
    {
        $task = Task::factory()->create();
        
        // Test with no comments
        $comments = TaskComment::getTaskCommentsWithFilters($task->id);
        $this->assertEquals(0, $comments->total());
        $this->assertTrue($comments->isEmpty());
        
        $this->assertFalse(TaskComment::hasComments($task->id));
        $this->assertEquals(0, TaskComment::getCommentCount($task->id));
    }
}
