{"__meta": {"id": "X9dcd5ff0bb577c712a1bccec89570fac", "datetime": "2025-06-22 12:24:59", "utime": 1750566299.119754, "method": "GET", "uri": "/tasks/88/comment/29", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[12:24:58] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1750566298.833883, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750566298.555353, "end": 1750566299.119775, "duration": 0.5644221305847168, "duration_str": "564ms", "measures": [{"label": "Booting", "start": 1750566298.555353, "relative_start": 0, "end": 1750566298.813969, "relative_end": 1750566298.813969, "duration": 0.25861597061157227, "duration_str": "259ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1750566298.813981, "relative_start": 0.25862812995910645, "end": 1750566299.119777, "relative_end": 1.9073486328125e-06, "duration": 0.30579590797424316, "duration_str": "306ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25202608, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 18, "templates": [{"name": "tasks.viewCommentList (\\resources\\views\\tasks\\viewCommentList.blade.php)", "param_count": 7, "params": ["title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "__currentLoopData", "creator", "loop", "comment"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "__currentLoopData", "creator", "loop", "comment"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "__currentLoopData", "creator", "loop", "comment"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "__currentLoopData", "creator", "loop", "comment"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "__currentLoopData", "creator", "loop", "comment"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}]}, "route": {"uri": "GET tasks/{task_id}/comment/{sprint_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@viewCommentList", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "tasks.viewCommentList", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=249\">\\app\\Http\\Controllers\\TaskController.php:249-306</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00582, "accumulated_duration_str": "5.82ms", "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00344, "duration_str": "3.44ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "kanban", "start_percent": 0, "width_percent": 59.107}, {"sql": "select * from `tasks` where `tasks`.`id` = '88' limit 1", "type": "query", "params": [], "bindings": ["88"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Task.php", "line": 43}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 258}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Task.php:43", "connection": "kanban", "start_percent": 59.107, "width_percent": 7.56}, {"sql": "select * from `sprint` where `sprint_id` = 29 limit 1", "type": "query", "params": [], "bindings": ["29"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Sprint.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 264}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Sprint.php:41", "connection": "kanban", "start_percent": 66.667, "width_percent": 7.045}, {"sql": "select `created_by` from `taskComment` where `task_id` = '88'", "type": "query", "params": [], "bindings": ["88"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\taskComment.php", "line": 39}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 272}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\taskComment.php:39", "connection": "kanban", "start_percent": 73.711, "width_percent": 6.701}, {"sql": "select count(*) as aggregate from `taskComment` where `task_id` = '88'", "type": "query", "params": [], "bindings": ["88"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\taskComment.php", "line": 89}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 282}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\taskComment.php:89", "connection": "kanban", "start_percent": 80.412, "width_percent": 5.842}, {"sql": "select * from `taskComment` where `task_id` = '88' order by `updated_at` desc limit 5 offset 0", "type": "query", "params": [], "bindings": ["88"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\taskComment.php", "line": 89}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 282}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\taskComment.php:89", "connection": "kanban", "start_percent": 86.254, "width_percent": 7.56}, {"sql": "select exists(select * from `taskComment` where `task_id` = '88') as `exists`", "type": "query", "params": [], "bindings": ["88"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\taskComment.php", "line": 100}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 290}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\taskComment.php:100", "connection": "kanban", "start_percent": 93.814, "width_percent": 6.186}]}, "models": {"data": {"App\\taskComment": 3, "App\\Sprint": 1, "App\\Task": 1, "App\\User": 1}, "count": 6}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/tasks/88/comment/29\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/tasks/88/comment/29", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-617694956 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-617694956\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-111652803 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-111652803\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1394463128 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/83</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImxoYkwyYXVndTJyRnJLcnlhNlJnSWc9PSIsInZhbHVlIjoiOEdLbVptOHJKWGJzRUs5S3VWYk53enZJYXp2RjhsWDBDVGJyaVNQcEp5VUFoeXIvcjlieTlxTkpPOS9rWUw4WnZUYlNOUVQxdytZSzNKWHY5ai9mT0FPRS9yaER3ODAybUZTM2pVN01VVmlJRGNjWWZRdytOc3ZGQU1Iay9qZVMiLCJtYWMiOiIxYTdkOWJmNzQ4NWMxNzhiZWYzNGE3MDY4Y2U4NGZiYjA4YWMyZTcxYmIxYzMwN2E0N2VhZjlmODM3MmNlMDg0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkR0U0VNdkFwZ0hGOHNmY09xWFVqMEE9PSIsInZhbHVlIjoidWZwSVVrb0EwWmsyeWJJTXlpWENVTE5uNUtmNTlvQUdnYU96cUliVU45UEhId2RmMkdxSWZpbnNMNTUxVGgrSllvT2JQUzRCdStCUlFmdzBhUS9zVVhORi80a0lWSk1CRHFGdFB2TkVnY0pWcjZPTFM5TGwrTlBrNXJ0cWowYkQiLCJtYWMiOiI1MjUxNmU1ZGNkZWRjZWU2MzQ0ODMzNThmYTFiMmI1OWY4NGI5MTRkYTlkMjhhNzJmODQzMzdkNWQ3Y2UxYTY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394463128\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1822296839 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54552</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/tasks/88/comment/29</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/tasks/88/comment/29</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/index.php/tasks/88/comment/29</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/83</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImxoYkwyYXVndTJyRnJLcnlhNlJnSWc9PSIsInZhbHVlIjoiOEdLbVptOHJKWGJzRUs5S3VWYk53enZJYXp2RjhsWDBDVGJyaVNQcEp5VUFoeXIvcjlieTlxTkpPOS9rWUw4WnZUYlNOUVQxdytZSzNKWHY5ai9mT0FPRS9yaER3ODAybUZTM2pVN01VVmlJRGNjWWZRdytOc3ZGQU1Iay9qZVMiLCJtYWMiOiIxYTdkOWJmNzQ4NWMxNzhiZWYzNGE3MDY4Y2U4NGZiYjA4YWMyZTcxYmIxYzMwN2E0N2VhZjlmODM3MmNlMDg0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkR0U0VNdkFwZ0hGOHNmY09xWFVqMEE9PSIsInZhbHVlIjoidWZwSVVrb0EwWmsyeWJJTXlpWENVTE5uNUtmNTlvQUdnYU96cUliVU45UEhId2RmMkdxSWZpbnNMNTUxVGgrSllvT2JQUzRCdStCUlFmdzBhUS9zVVhORi80a0lWSk1CRHFGdFB2TkVnY0pWcjZPTFM5TGwrTlBrNXJ0cWowYkQiLCJtYWMiOiI1MjUxNmU1ZGNkZWRjZWU2MzQ0ODMzNThmYTFiMmI1OWY4NGI5MTRkYTlkMjhhNzJmODQzMzdkNWQ3Y2UxYTY4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1750566298.5554</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1750566298</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822296839\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1025578458 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BNgxUsf2gIxtawp1Y1q9JWoBx2mGAvACnHN2kCr1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025578458\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 04:24:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkZpVktMN1Q4VlFwN1BlS0l0WTFocEE9PSIsInZhbHVlIjoiMnlWS1JmZ0k2ZGNtMHpGaW8zQlZManhTeTJTRHhycDUvN3VWZ1hjakwraHp6YWhFdmk5V2R3c0JmVGplYWZzWFVUNnlrdHVPYVM3cDkwcW5kV2hBOUxSMG5iRUlNK1JRTGlnb0NNZ09HZzVFR0wrendtcnVXNk5aWW9vQnRnRVYiLCJtYWMiOiJkMzQ5ZmUyYWZmNDEyNDQ2NzUxYzEzYWRjYWE4NWZiN2ZhMGI4MTFmN2U0MDgyMzMyOTJiNmRiZjU3ZmVkYWRjIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:24:59 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlVWUnhQcWZiQmxNbUpTdWJPUTlkZkE9PSIsInZhbHVlIjoicDdkOFdTV0lDVGFQRFY2SXI5YW1oMnFCQU80SUM1NEE4bjlwYVVSRmErNmJIY214MU9nanQvZDR5SDZBUnIwY0diMFdzSm5JeGE5M1dUeUlEM1I3amtpbFVLRi9QSGlnUFY3TDU0TEFLckpHLzgxY0hacVlSdzQ2bkdwaUJiSHciLCJtYWMiOiJiZjg2MzEwZTZjMTU4NWJjYjgwODYyNGI1MzcyMTFhYTg1MzU5M2RkNjMyYTVlYWJmOTQyYjljY2JkYTkyZGE1IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:24:59 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkZpVktMN1Q4VlFwN1BlS0l0WTFocEE9PSIsInZhbHVlIjoiMnlWS1JmZ0k2ZGNtMHpGaW8zQlZManhTeTJTRHhycDUvN3VWZ1hjakwraHp6YWhFdmk5V2R3c0JmVGplYWZzWFVUNnlrdHVPYVM3cDkwcW5kV2hBOUxSMG5iRUlNK1JRTGlnb0NNZ09HZzVFR0wrendtcnVXNk5aWW9vQnRnRVYiLCJtYWMiOiJkMzQ5ZmUyYWZmNDEyNDQ2NzUxYzEzYWRjYWE4NWZiN2ZhMGI4MTFmN2U0MDgyMzMyOTJiNmRiZjU3ZmVkYWRjIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:24:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlVWUnhQcWZiQmxNbUpTdWJPUTlkZkE9PSIsInZhbHVlIjoicDdkOFdTV0lDVGFQRFY2SXI5YW1oMnFCQU80SUM1NEE4bjlwYVVSRmErNmJIY214MU9nanQvZDR5SDZBUnIwY0diMFdzSm5JeGE5M1dUeUlEM1I3amtpbFVLRi9QSGlnUFY3TDU0TEFLckpHLzgxY0hacVlSdzQ2bkdwaUJiSHciLCJtYWMiOiJiZjg2MzEwZTZjMTU4NWJjYjgwODYyNGI1MzcyMTFhYTg1MzU5M2RkNjMyYTVlYWJmOTQyYjljY2JkYTkyZGE1IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:24:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1000849386 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/tasks/88/comment/29</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1000849386\", {\"maxDepth\":0})</script>\n"}}