@extends('layouts.app2')
@include('inc.success')
@include('inc.style')

@include('inc.navbar')

@section('content')
@include('inc.title')
<br><br>
<div class="container">
    <h3>Task Details</h3>
    <p><strong>Task Title:</strong> {{ $task->title }}</p>
    <p><strong>Description:</strong> {{ $task->description }}</p>
    <p><strong>End Date:</strong> {{ \Carbon\Carbon::parse($task->end_date)->format('Y-m-d') }}</p>
    @if ($isSprintOverdue)
        <div class="alert alert-warning">The sprint is overdue!</div>
    @else
        @if ($isTaskOverdue)
            <div class="alert alert-danger">This task is overdue!</div>
        @endif
    @endif

    <hr>
    <h3>Comments
        @if ($commentCount > 0)
            <small class="text-muted">({{ $commentCount }} total)</small>
        @endif
    </h3>

        <form method="GET" action="{{ route('tasks.viewCommentList', ['task_id' => $task->id, 'sprint_id' => $task->sprint_id]) }}">
            <div class="form-group">
                <label for="created_by">Filter by Created By</label>
                <select name="created_by" id="created_by" class="form-control" onchange="this.form.submit()">
                    <option value="">All</option>
                    @foreach ($uniqueCreators as $creator)
                        <option value="{{ $creator }}" {{ request('created_by') == $creator ? 'selected' : '' }}>
                            {{ $creator }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div class="form-group">
                <label for="sort_date">Sort by Date</label>
                <select name="sort_date" id="sort_date" class="form-control" onchange="this.form.submit()">
                    <option value="desc" {{ request('sort_date') == 'desc' ? 'selected' : '' }}>Newest</option>
                    <option value="asc" {{ request('sort_date') == 'asc' ? 'selected' : '' }}>Oldest</option>
                </select>
            </div>
        </form>

    <hr>
    @if (!$hasComments || $comments->isEmpty())
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> No comments available for this task.
            @if ($commentCount > 0)
                <small class="text-muted">({{ $commentCount }} total comments, but none match current filters)</small>
            @endif
        </div>
    @else
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Comment</th>
                    <th>Created By</th>
                    <th>Updated At</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($comments as $comment)
                    <tr>
                        <td>{{ $comment->comment }}</td>
                        <td>{{ $comment->created_by }}</td>
                        <td>{{ \Carbon\Carbon::parse($comment->updated_at)->format('Y-m-d') }}</td>
                        <td>
                            @if (!$isTaskOverdue && !$isSprintOverdue && $comment->created_by === auth()->user()->username)
                                <a href="{{ route('tasks.editComment', $comment->id) }}" class="btn btn-warning btn-sm">Edit</a>
                                <form action="{{ route('tasks.deleteComment', $comment->id) }}" method="POST" style="display:inline;">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure want to delete this comment?')">Delete</button>
                                </form>
                            @else
                                <span class="text-muted">No Actions Available</span>
                            @endif
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @endif

    <div class="pagination-container">
        {{ $comments->appends(request()->query())->links('pagination::bootstrap-4') }}
    </div>

    @if (!$isTaskOverdue && !$isSprintOverdue)
        <a href="{{ route('tasks.createComment', ['task_id' => $task->id, $task->sprint_id]) }}" class="btn btn-success">Add Comment</a>
    @endif
</div>
@endsection