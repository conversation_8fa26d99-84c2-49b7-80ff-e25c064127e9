{"__meta": {"id": "Xf7d0e132ed6dc11e546864f4f3c6ff3c", "datetime": "2025-06-22 12:20:29", "utime": 1750566029.481162, "method": "GET", "uri": "/tasks/89/comment/29", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[12:20:28] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1750566028.861352, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750566028.53592, "end": 1750566029.481188, "duration": 0.9452681541442871, "duration_str": "945ms", "measures": [{"label": "Booting", "start": 1750566028.53592, "relative_start": 0, "end": 1750566028.838857, "relative_end": 1750566028.838857, "duration": 0.30293703079223633, "duration_str": "303ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1750566028.838873, "relative_start": 0.30295300483703613, "end": 1750566029.481191, "relative_end": 2.86102294921875e-06, "duration": 0.6423180103302002, "duration_str": "642ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25179936, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 18, "templates": [{"name": "tasks.viewCommentList (\\resources\\views\\tasks\\viewCommentList.blade.php)", "param_count": 8, "params": ["title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount", "__currentLoopData", "loop"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount", "__currentLoopData", "loop"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount", "__currentLoopData", "loop"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount", "__currentLoopData", "loop"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount", "__currentLoopData", "loop"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "hasComments", "commentCount", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}]}, "route": {"uri": "GET tasks/{task_id}/comment/{sprint_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@viewCommentList", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "tasks.viewCommentList", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=249\">\\app\\Http\\Controllers\\TaskController.php:249-303</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02189, "accumulated_duration_str": "21.89ms", "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00364, "duration_str": "3.64ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "kanban", "start_percent": 0, "width_percent": 16.629}, {"sql": "select * from `tasks` where `tasks`.`id` = '89' limit 1", "type": "query", "params": [], "bindings": ["89"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Task.php", "line": 43}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 258}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Task.php:43", "connection": "kanban", "start_percent": 16.629, "width_percent": 2.147}, {"sql": "select * from `sprint` where `sprint_id` = 29 limit 1", "type": "query", "params": [], "bindings": ["29"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Sprint.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 264}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Sprint.php:41", "connection": "kanban", "start_percent": 18.776, "width_percent": 2.01}, {"sql": "select `created_by` from `taskComment` where `task_id` = '89'", "type": "query", "params": [], "bindings": ["89"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\taskComment.php", "line": 39}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 272}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.01573, "duration_str": "15.73ms", "stmt_id": "\\app\\taskComment.php:39", "connection": "kanban", "start_percent": 20.786, "width_percent": 71.859}, {"sql": "select count(*) as aggregate from `taskComment` where `task_id` = '89'", "type": "query", "params": [], "bindings": ["89"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\taskComment.php", "line": 89}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 282}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\taskComment.php:89", "connection": "kanban", "start_percent": 92.645, "width_percent": 2.467}, {"sql": "select exists(select * from `taskComment` where `task_id` = '89') as `exists`", "type": "query", "params": [], "bindings": ["89"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\taskComment.php", "line": 100}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 285}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\taskComment.php:100", "connection": "kanban", "start_percent": 95.112, "width_percent": 2.924}, {"sql": "select count(*) as aggregate from `taskComment` where `task_id` = '89'", "type": "query", "params": [], "bindings": ["89"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\taskComment.php", "line": 111}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 286}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\taskComment.php:111", "connection": "kanban", "start_percent": 98.036, "width_percent": 1.964}]}, "models": {"data": {"App\\Sprint": 1, "App\\Task": 1, "App\\User": 1}, "count": 3}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/tasks/89/comment/29\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/tasks/89/comment/29", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2036094937 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2036094937\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1811175898 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1811175898\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-996624267 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/83</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlNQcGxBWms3SDlRU0xUd1ZYS0RmVGc9PSIsInZhbHVlIjoicXM5VlltZGFENlllSGRwZlZwWnlYQ3ZVajIybFQwSStnMzVNa21oL0l4WU44Ui9mV1RJbm5NNHpPRS9POTFySUhhZ0I1T3YwNzRFU1NJL2w3TlErMEtCTm1Da0VpL1Y1azNYL0lQcjU4MEZYZ011WkZSU0dJNXQ1NTEwdEEwUG0iLCJtYWMiOiJkYTJjN2EwNTYwMzRmYjUxZjM2M2I5ZmI4MmJmZTlhY2MyOTdiMDMwYTk5MjkyNWRiNGUyMTYxZTFmMDYxNWE0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InNROUNiSy9xbThIZ2d2dDlpL2c5eFE9PSIsInZhbHVlIjoicHFEUUN1S2pBM2tuRlVsbVBqMS9hNzl2NkZDbWg3RzRIbVBjeWw1VGNMOWNHVXNwN2JRWENwbEZVMzh5djlpUmV2WTRYUWJtd2NjZlZwbm9IOC82Y0h3bmQ0YUsvYWxYZkR4MjhyRkpjMEd6cTNxOFl6S3ljZ0lmcmJUQmdmdnUiLCJtYWMiOiJiYmQ3Yjk1MzJhMDI1ZDQ2ODVkNGQ5MTYyMTEzNjg0MGE4MDdkMzczMjMwOTYyNmE3ZWY5NTMxYTlmNmE1YmQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-996624267\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2060641873 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54252</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/tasks/89/comment/29</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/tasks/89/comment/29</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/index.php/tasks/89/comment/29</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/83</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlNQcGxBWms3SDlRU0xUd1ZYS0RmVGc9PSIsInZhbHVlIjoicXM5VlltZGFENlllSGRwZlZwWnlYQ3ZVajIybFQwSStnMzVNa21oL0l4WU44Ui9mV1RJbm5NNHpPRS9POTFySUhhZ0I1T3YwNzRFU1NJL2w3TlErMEtCTm1Da0VpL1Y1azNYL0lQcjU4MEZYZ011WkZSU0dJNXQ1NTEwdEEwUG0iLCJtYWMiOiJkYTJjN2EwNTYwMzRmYjUxZjM2M2I5ZmI4MmJmZTlhY2MyOTdiMDMwYTk5MjkyNWRiNGUyMTYxZTFmMDYxNWE0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InNROUNiSy9xbThIZ2d2dDlpL2c5eFE9PSIsInZhbHVlIjoicHFEUUN1S2pBM2tuRlVsbVBqMS9hNzl2NkZDbWg3RzRIbVBjeWw1VGNMOWNHVXNwN2JRWENwbEZVMzh5djlpUmV2WTRYUWJtd2NjZlZwbm9IOC82Y0h3bmQ0YUsvYWxYZkR4MjhyRkpjMEd6cTNxOFl6S3ljZ0lmcmJUQmdmdnUiLCJtYWMiOiJiYmQ3Yjk1MzJhMDI1ZDQ2ODVkNGQ5MTYyMTEzNjg0MGE4MDdkMzczMjMwOTYyNmE3ZWY5NTMxYTlmNmE1YmQwIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1750566028.5359</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1750566028</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2060641873\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1788057676 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BNgxUsf2gIxtawp1Y1q9JWoBx2mGAvACnHN2kCr1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1788057676\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 04:20:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkpaaXVzU1hhc2dGdVdTbzFkeC8venc9PSIsInZhbHVlIjoiaTRnRzBCTU5HTFV0dG9JWlFvdVhRQWZ5TDlVVkMrUVlTYVg4dWo5dld1MjVxRnFQbHE1clIxUktic1h1NUtIS1lUcFZmRHZVRmRyaTRkanNxV205Zmg5OEpZTW5ZRWVrbVo5N2E5ZHV0WWJsZ3pPR0MwMytsMld0OGxQZUhLRkgiLCJtYWMiOiJiMGY5ZDBiNjkzOWVhMzc0Mzg3OWU4NTg5YzFiYmEzZTM3YmYzNjE5YjQ3MTMwOTJjMzgwMDFkZDFmM2JjYjRkIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:20:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkZLaVNlZ3ErVzloWC9xeDZKaVNjalE9PSIsInZhbHVlIjoiQnFadG5NdlRUT29COFhrYlJzRHN3T0hxSXlrNHhMdFdLc0FSWXdnWTVjL09RTHNQMlZmbnpnTmd2eGpYZ1dBUWp3VGU2UGxiRXptUllBS1ZPZ1BRaGUrZFUwUElwVkRzcm9lVU1wS3luV0w2SDRMLzE3ZW9iZFhOMElJWUg1Q1kiLCJtYWMiOiJjN2JiMmFjZWVjYWY3MzJiZmM5Y2ZkZWZjYmFkY2JiYjNkMTc3MWIxY2Y1MGE0ZDlkMjk0NjQ0YzcyY2NlYTk3IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:20:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkpaaXVzU1hhc2dGdVdTbzFkeC8venc9PSIsInZhbHVlIjoiaTRnRzBCTU5HTFV0dG9JWlFvdVhRQWZ5TDlVVkMrUVlTYVg4dWo5dld1MjVxRnFQbHE1clIxUktic1h1NUtIS1lUcFZmRHZVRmRyaTRkanNxV205Zmg5OEpZTW5ZRWVrbVo5N2E5ZHV0WWJsZ3pPR0MwMytsMld0OGxQZUhLRkgiLCJtYWMiOiJiMGY5ZDBiNjkzOWVhMzc0Mzg3OWU4NTg5YzFiYmEzZTM3YmYzNjE5YjQ3MTMwOTJjMzgwMDFkZDFmM2JjYjRkIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:20:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkZLaVNlZ3ErVzloWC9xeDZKaVNjalE9PSIsInZhbHVlIjoiQnFadG5NdlRUT29COFhrYlJzRHN3T0hxSXlrNHhMdFdLc0FSWXdnWTVjL09RTHNQMlZmbnpnTmd2eGpYZ1dBUWp3VGU2UGxiRXptUllBS1ZPZ1BRaGUrZFUwUElwVkRzcm9lVU1wS3luV0w2SDRMLzE3ZW9iZFhOMElJWUg1Q1kiLCJtYWMiOiJjN2JiMmFjZWVjYWY3MzJiZmM5Y2ZkZWZjYmFkY2JiYjNkMTc3MWIxY2Y1MGE0ZDlkMjk0NjQ0YzcyY2NlYTk3IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:20:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1882527685 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/tasks/89/comment/29</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1882527685\", {\"maxDepth\":0})</script>\n"}}