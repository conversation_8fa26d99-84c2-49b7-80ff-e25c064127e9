{"__meta": {"id": "X805db835c69a278a4c8a3c509c8ec034", "datetime": "2025-06-22 12:20:18", "utime": 1750566018.756706, "method": "GET", "uri": "/profeature/userstory/30", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[12:20:18] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1750566018.492352, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750566018.179982, "end": 1750566018.756724, "duration": 0.5767421722412109, "duration_str": "577ms", "measures": [{"label": "Booting", "start": 1750566018.179982, "relative_start": 0, "end": 1750566018.473082, "relative_end": 1750566018.473082, "duration": 0.29310011863708496, "duration_str": "293ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1750566018.473092, "relative_start": 0.2931101322174072, "end": 1750566018.756726, "relative_end": 1.9073486328125e-06, "duration": 0.2836339473724365, "duration_str": "284ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25094824, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 18, "templates": [{"name": "profeature.index3 (\\resources\\views\\profeature\\index3.blade.php)", "param_count": 6, "params": ["userstories", "nfrData", "sprint_id", "pros", "statuses", "title"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title"], "type": "blade"}, {"name": "inc.dashboard (\\resources\\views\\inc\\dashboard.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}]}, "route": {"uri": "GET profeature/userstory/{sprint_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\ProductFeatureController@index3", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "profeature.index3", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProductFeatureController.php&line=97\">\\app\\Http\\Controllers\\ProductFeatureController.php:97-146</a>"}, "queries": {"nb_statements": 13, "nb_failed_statements": 0, "accumulated_duration": 0.00757, "accumulated_duration_str": "7.57ms", "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0033900000000000002, "duration_str": "3.39ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "kanban", "start_percent": 0, "width_percent": 44.782}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 101}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:101", "connection": "kanban", "start_percent": 44.782, "width_percent": 5.284}, {"sql": "select * from `projects` where `team_name` in ('999', 'iv<PERSON>\\'s team')", "type": "query", "params": [], "bindings": ["999", "ivlyn&#039;s team"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 102}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:102", "connection": "kanban", "start_percent": 50.066, "width_percent": 4.227}, {"sql": "select * from `statuses`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 104}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:104", "connection": "kanban", "start_percent": 54.293, "width_percent": 7.662}, {"sql": "select * from `sprint` where `sprint_id` = '30' limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 107}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:107", "connection": "kanban", "start_percent": 61.955, "width_percent": 4.359}, {"sql": "select * from `user_stories` where `sprint_id` = '30'", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 110}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:110", "connection": "kanban", "start_percent": 66.314, "width_percent": 4.491}, {"sql": "select * from `user_story_general_nfr` where `user_story_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 115}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:115", "connection": "kanban", "start_percent": 70.806, "width_percent": 5.02}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` = 6 limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 119}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:119", "connection": "kanban", "start_percent": 75.826, "width_percent": 4.095}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` = 8 limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 129}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:129", "connection": "kanban", "start_percent": 79.921, "width_percent": 4.624}, {"sql": "select * from `user_story_general_nfr` where `user_story_id` = 88", "type": "query", "params": [], "bindings": ["88"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 115}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:115", "connection": "kanban", "start_percent": 84.544, "width_percent": 3.963}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` = 6 limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 119}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00026000000000000003, "duration_str": "260μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:119", "connection": "kanban", "start_percent": 88.507, "width_percent": 3.435}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` = 8 limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 129}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:129", "connection": "kanban", "start_percent": 91.942, "width_percent": 4.491}, {"sql": "select * from `user_story_general_nfr` where `user_story_id` = 89", "type": "query", "params": [], "bindings": ["89"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 115}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:115", "connection": "kanban", "start_percent": 96.433, "width_percent": 3.567}]}, "models": {"data": {"App\\SpecificNFR": 2, "App\\GeneralNFR": 2, "App\\UserStoryGeneralNfr": 2, "App\\UserStory": 3, "App\\Sprint": 1, "App\\Status": 83, "App\\Project": 3, "App\\User": 1}, "count": 97}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/profeature/userstory/30\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/profeature/userstory/30", "status_code": "<pre class=sf-dump id=sf-dump-1658108918 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1658108918\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-474049722 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-474049722\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1684473728 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1684473728\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1264468497 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/84</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjE5MmFRTFZYRGJhWTkvY0c4cXhkM1E9PSIsInZhbHVlIjoia3lDTnlPZVI5V3JQQmV0eVdHenYzYm41dStxVmNWb1A4YlRFYisvWFdwbVV0UGJBYjZKQ20yb0w0M28vamw0NjRSOUJxbmVRVEp1N1o0S3BQT0FmQTZERXNrYWVUNjlVOXhjeVFIZWRFekZpVk1FRGkwb0dJd2pvemR3VTNwQVAiLCJtYWMiOiJiMjNkYmU0NjgyNjMwOWJjMGFkMjhhMDkyOGYwY2UxZjZmNWQ1ZjBlMzIwZWU2NjhmNWM2MTdlNTk1ZjkxNDg3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjArRWI3K0NzUVBkdFBLOGVxZFpNVmc9PSIsInZhbHVlIjoiOU1DS2RDUDhlSHhUc3dKcDNSWnQwN0lqWWJDa3NYK0RXUkROdXBPOW9yWkxWcktlRW43L0YxRmlFK3NlM0h6UWZlc0F1bkg4bTQ2VzlxWFVES2t6SVhaa3FiRndkRFU3czU2dVpybkVQRklSQUhOVGljRnhZK2N5d1hFb05xeloiLCJtYWMiOiJhNzExODk2ZWNkNGJjYzk4YzRkZTI5YTNjZTMzY2Y1OWUyMzMyM2JjNTI5OTMyM2UwZmRlNzEyNTc3NWFhOGI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264468497\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54137</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/profeature/userstory/30</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/profeature/userstory/30</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"34 characters\">/index.php/profeature/userstory/30</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/84</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjE5MmFRTFZYRGJhWTkvY0c4cXhkM1E9PSIsInZhbHVlIjoia3lDTnlPZVI5V3JQQmV0eVdHenYzYm41dStxVmNWb1A4YlRFYisvWFdwbVV0UGJBYjZKQ20yb0w0M28vamw0NjRSOUJxbmVRVEp1N1o0S3BQT0FmQTZERXNrYWVUNjlVOXhjeVFIZWRFekZpVk1FRGkwb0dJd2pvemR3VTNwQVAiLCJtYWMiOiJiMjNkYmU0NjgyNjMwOWJjMGFkMjhhMDkyOGYwY2UxZjZmNWQ1ZjBlMzIwZWU2NjhmNWM2MTdlNTk1ZjkxNDg3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjArRWI3K0NzUVBkdFBLOGVxZFpNVmc9PSIsInZhbHVlIjoiOU1DS2RDUDhlSHhUc3dKcDNSWnQwN0lqWWJDa3NYK0RXUkROdXBPOW9yWkxWcktlRW43L0YxRmlFK3NlM0h6UWZlc0F1bkg4bTQ2VzlxWFVES2t6SVhaa3FiRndkRFU3czU2dVpybkVQRklSQUhOVGljRnhZK2N5d1hFb05xeloiLCJtYWMiOiJhNzExODk2ZWNkNGJjYzk4YzRkZTI5YTNjZTMzY2Y1OWUyMzMyM2JjNTI5OTMyM2UwZmRlNzEyNTc3NWFhOGI5IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1750566018.18</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1750566018</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1496135204 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BNgxUsf2gIxtawp1Y1q9JWoBx2mGAvACnHN2kCr1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1496135204\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2088233446 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 04:20:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlVFbzZCZGxDOE1WVkFwMkpLNkl5QUE9PSIsInZhbHVlIjoiMHdFMGtNTjQxMTNhWit1RWpBdERseHpOTW5MeVNKbk54RGtVd2JadGlJWkNNVVNxNDlubEJ2ZHNBZG5DV2UzMVJGL1VpWkJFN0ZKczhJRlRJSGpyUEZTUi93SEtyYTVUdnJzbXZjSUxsYnFXQlpPMFpHOUFQOXdqTUJyUVpHZW4iLCJtYWMiOiI0ZjBiMjlhZWU4YTNmZDBjOGI4N2NhZWIwZjM3Yjk3NzI0NjNmYzQ2ZWJlNjdiMGEyY2Y4MDc2MGY4MWY2YTdiIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:20:18 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im1qcW5rMENrTm9aMW8vOXVsbkpweUE9PSIsInZhbHVlIjoicDhBa2txQ0tocmRQYUV0OWJBcmJiYW5jbW9EK0xMejZyWi9rV0hxbERUZWR1T20yZldWa3ZWNGY4NmhZQjBLWTdjQ1pkazVYWG1zQU1JUG5Na2JNZGZUZGcxN01zd2xLc2NBOHpZRVNod3ZTR1Z1ck11Y0ROK3oxSlhqMmNaeDQiLCJtYWMiOiIxZDJmZGQyYmVkMWMyNDg1NDViOTc5N2JkNDRlNTQyNTg1ZGY5YWMxNmNiMTk0Yjc3ZThiOTY5NGI1ZGZiYWMxIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:20:18 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlVFbzZCZGxDOE1WVkFwMkpLNkl5QUE9PSIsInZhbHVlIjoiMHdFMGtNTjQxMTNhWit1RWpBdERseHpOTW5MeVNKbk54RGtVd2JadGlJWkNNVVNxNDlubEJ2ZHNBZG5DV2UzMVJGL1VpWkJFN0ZKczhJRlRJSGpyUEZTUi93SEtyYTVUdnJzbXZjSUxsYnFXQlpPMFpHOUFQOXdqTUJyUVpHZW4iLCJtYWMiOiI0ZjBiMjlhZWU4YTNmZDBjOGI4N2NhZWIwZjM3Yjk3NzI0NjNmYzQ2ZWJlNjdiMGEyY2Y4MDc2MGY4MWY2YTdiIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:20:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im1qcW5rMENrTm9aMW8vOXVsbkpweUE9PSIsInZhbHVlIjoicDhBa2txQ0tocmRQYUV0OWJBcmJiYW5jbW9EK0xMejZyWi9rV0hxbERUZWR1T20yZldWa3ZWNGY4NmhZQjBLWTdjQ1pkazVYWG1zQU1JUG5Na2JNZGZUZGcxN01zd2xLc2NBOHpZRVNod3ZTR1Z1ck11Y0ROK3oxSlhqMmNaeDQiLCJtYWMiOiIxZDJmZGQyYmVkMWMyNDg1NDViOTc5N2JkNDRlNTQyNTg1ZGY5YWMxNmNiMTk0Yjc3ZThiOTY5NGI1ZGZiYWMxIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:20:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088233446\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1135871672 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/profeature/userstory/30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1135871672\", {\"maxDepth\":0})</script>\n"}}