{"__meta": {"id": "Xfbe817ca4ab21ff5f6699bd80c5486e5", "datetime": "2025-06-22 12:23:53", "utime": 1750566233.998186, "method": "GET", "uri": "/profeature/userstory/29", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[12:23:53] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1750566233.709764, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750566233.400597, "end": 1750566233.998207, "duration": 0.5976099967956543, "duration_str": "598ms", "measures": [{"label": "Booting", "start": 1750566233.400597, "relative_start": 0, "end": 1750566233.688793, "relative_end": 1750566233.688793, "duration": 0.2881958484649658, "duration_str": "288ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1750566233.688806, "relative_start": 0.2882089614868164, "end": 1750566233.998209, "relative_end": 1.9073486328125e-06, "duration": 0.3094029426574707, "duration_str": "309ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25088544, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 18, "templates": [{"name": "profeature.index3 (\\resources\\views\\profeature\\index3.blade.php)", "param_count": 6, "params": ["userstories", "nfrData", "sprint_id", "pros", "statuses", "title"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title"], "type": "blade"}, {"name": "inc.dashboard (\\resources\\views\\inc\\dashboard.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "userstories", "nfrData", "sprint_id", "pros", "statuses", "title", "__empty_1", "__currentLoopData", "userstory", "loop", "hasNfr", "userNames", "status", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}]}, "route": {"uri": "GET profeature/userstory/{sprint_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\ProductFeatureController@index3", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "profeature.index3", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProductFeatureController.php&line=97\">\\app\\Http\\Controllers\\ProductFeatureController.php:97-146</a>"}, "queries": {"nb_statements": 12, "nb_failed_statements": 0, "accumulated_duration": 0.009340000000000001, "accumulated_duration_str": "9.34ms", "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00379, "duration_str": "3.79ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "kanban", "start_percent": 0, "width_percent": 40.578}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 101}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:101", "connection": "kanban", "start_percent": 40.578, "width_percent": 5.032}, {"sql": "select * from `projects` where `team_name` in ('999', 'iv<PERSON>\\'s team')", "type": "query", "params": [], "bindings": ["999", "ivlyn&#039;s team"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 102}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:102", "connection": "kanban", "start_percent": 45.61, "width_percent": 5.353}, {"sql": "select * from `statuses`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 104}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:104", "connection": "kanban", "start_percent": 50.964, "width_percent": 6.317}, {"sql": "select * from `sprint` where `sprint_id` = '29' limit 1", "type": "query", "params": [], "bindings": ["29"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 107}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:107", "connection": "kanban", "start_percent": 57.281, "width_percent": 5.032}, {"sql": "select * from `user_stories` where `sprint_id` = '29'", "type": "query", "params": [], "bindings": ["29"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 110}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:110", "connection": "kanban", "start_percent": 62.313, "width_percent": 5.889}, {"sql": "select * from `user_story_general_nfr` where `user_story_id` = 83", "type": "query", "params": [], "bindings": ["83"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 115}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:115", "connection": "kanban", "start_percent": 68.201, "width_percent": 6.103}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` = 6 limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 119}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:119", "connection": "kanban", "start_percent": 74.304, "width_percent": 6.959}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` = 8 limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 129}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:129", "connection": "kanban", "start_percent": 81.263, "width_percent": 4.39}, {"sql": "select * from `user_story_general_nfr` where `user_story_id` = 99", "type": "query", "params": [], "bindings": ["99"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 115}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:115", "connection": "kanban", "start_percent": 85.653, "width_percent": 5.246}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` = 32 limit 1", "type": "query", "params": [], "bindings": ["32"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 119}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:119", "connection": "kanban", "start_percent": 90.899, "width_percent": 4.604}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` = 52 limit 1", "type": "query", "params": [], "bindings": ["52"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 129}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:129", "connection": "kanban", "start_percent": 95.503, "width_percent": 4.497}]}, "models": {"data": {"App\\SpecificNFR": 2, "App\\GeneralNFR": 2, "App\\UserStoryGeneralNfr": 2, "App\\UserStory": 2, "App\\Sprint": 1, "App\\Status": 83, "App\\Project": 3, "App\\User": 1}, "count": 96}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/profeature/userstory/29\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/profeature/userstory/29", "status_code": "<pre class=sf-dump id=sf-dump-436491927 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-436491927\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-143449129 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-143449129\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1878590614 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1878590614\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-659930373 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/profeature/SAgile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlA0YjRTZXQ4WWFBdXVrTDI5ZWlBS3c9PSIsInZhbHVlIjoibmx0RkxaUk45MlZtdGtqamxBUE15YWIxcXVEdnVQdXdZSDVBMFFQMkJNL3pCWE5OS3BFOUNNdm5COVJ0VU5nOVE0RVRZUDVxdVI2c2JsZFBjVkR2M1hQSklUREVSdTR2NDdZdmhtMHdKcFU3YjNrL1Q1RXIvc205Um0rWTgwMWQiLCJtYWMiOiJhOTY0M2Q1Y2Q2N2ExMzM5M2RjYmMyNThlMGJkYWUwNDViMzNmZGE3NjBmMjAwNDA0YjdmMGI1NzExZjkxYmNmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InRTSEdvMjhXNGRkanliY0JRT3U5Mmc9PSIsInZhbHVlIjoiQU5lclpDTmdJTk85azRCOWVHNDR6SUlLaSt1OW5iZlNVU044M0E2emJsZlNLS2RHY2xHbmJwU0dGYm9YVGxHbysybGR2ZUNKbWxzbTEyZTRJQnBGb05WSW9TUVE3aXlSbXM4dk5xOUYyNmllajFqdE10SVh6ZGxUeERVWGNsQWMiLCJtYWMiOiI2MWFmNTU0OWUxYmQxMmQzNzk4MGU2YjIxYjBkYjQ5OGFkNDI3ZjU3YjMxOWI1YjkzNTE5ZjUwNTE5ZWIzZmVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659930373\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1159716144 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54405</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/profeature/userstory/29</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/profeature/userstory/29</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"34 characters\">/index.php/profeature/userstory/29</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/profeature/SAgile</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlA0YjRTZXQ4WWFBdXVrTDI5ZWlBS3c9PSIsInZhbHVlIjoibmx0RkxaUk45MlZtdGtqamxBUE15YWIxcXVEdnVQdXdZSDVBMFFQMkJNL3pCWE5OS3BFOUNNdm5COVJ0VU5nOVE0RVRZUDVxdVI2c2JsZFBjVkR2M1hQSklUREVSdTR2NDdZdmhtMHdKcFU3YjNrL1Q1RXIvc205Um0rWTgwMWQiLCJtYWMiOiJhOTY0M2Q1Y2Q2N2ExMzM5M2RjYmMyNThlMGJkYWUwNDViMzNmZGE3NjBmMjAwNDA0YjdmMGI1NzExZjkxYmNmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InRTSEdvMjhXNGRkanliY0JRT3U5Mmc9PSIsInZhbHVlIjoiQU5lclpDTmdJTk85azRCOWVHNDR6SUlLaSt1OW5iZlNVU044M0E2emJsZlNLS2RHY2xHbmJwU0dGYm9YVGxHbysybGR2ZUNKbWxzbTEyZTRJQnBGb05WSW9TUVE3aXlSbXM4dk5xOUYyNmllajFqdE10SVh6ZGxUeERVWGNsQWMiLCJtYWMiOiI2MWFmNTU0OWUxYmQxMmQzNzk4MGU2YjIxYjBkYjQ5OGFkNDI3ZjU3YjMxOWI1YjkzNTE5ZjUwNTE5ZWIzZmVmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1750566233.4006</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1750566233</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1159716144\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BNgxUsf2gIxtawp1Y1q9JWoBx2mGAvACnHN2kCr1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1235952473 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 04:23:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InE2OUhFTldOc3ozZ0ZINU5mejdVTWc9PSIsInZhbHVlIjoibkN3aTdDK054d3h6ajFvamZYLy9Rdnd0VTJNN21CejdtYjB0V1g1S09rU09pNkNBRzZaQzNFOXAzdk91UnJmZ2hZZzFFaWZFb2w5UDBmNUEzZllDb0EvY3JWYVVRRmJJOWdQa0s3NDR4bjhicHhBVlMrL0hUZWRDckRDWVRCTUwiLCJtYWMiOiI1ZGE4YTRlM2RkYmU4Y2IzZDRjY2Y1OTg5YjI5NzFlZDU0Y2M2MDZiOTUyNjQzNTI0OTE0ZTJhMzcyOTRkMjUwIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:23:53 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ijg0Nnp3MVl1cHlDblA1KzN4bDh3YlE9PSIsInZhbHVlIjoiK3RYL285WmpGOHdRajRXOXJZalE4cUl3VWZvSVlrc2VJL0lmK096VHZuT2hWSHNTSWNDSWlNV1dzNGE4VWZscG5oNk5jV2tHRzdXdXdpWXhyUWhmYU1VUWMwenNydW9xMytDVktuTU9HeUFhbkFoZWY0RURvMjJWTmZQaWhVUEgiLCJtYWMiOiJmZDlkMGJkYTkyY2M2M2Y0NWY0NGM4ZjAzN2EwNmQ4OTlmODBmN2RlOTM5MWI3MzRlMGI0MGI1OTE3NGUwNWQzIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:23:53 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InE2OUhFTldOc3ozZ0ZINU5mejdVTWc9PSIsInZhbHVlIjoibkN3aTdDK054d3h6ajFvamZYLy9Rdnd0VTJNN21CejdtYjB0V1g1S09rU09pNkNBRzZaQzNFOXAzdk91UnJmZ2hZZzFFaWZFb2w5UDBmNUEzZllDb0EvY3JWYVVRRmJJOWdQa0s3NDR4bjhicHhBVlMrL0hUZWRDckRDWVRCTUwiLCJtYWMiOiI1ZGE4YTRlM2RkYmU4Y2IzZDRjY2Y1OTg5YjI5NzFlZDU0Y2M2MDZiOTUyNjQzNTI0OTE0ZTJhMzcyOTRkMjUwIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:23:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ijg0Nnp3MVl1cHlDblA1KzN4bDh3YlE9PSIsInZhbHVlIjoiK3RYL285WmpGOHdRajRXOXJZalE4cUl3VWZvSVlrc2VJL0lmK096VHZuT2hWSHNTSWNDSWlNV1dzNGE4VWZscG5oNk5jV2tHRzdXdXdpWXhyUWhmYU1VUWMwenNydW9xMytDVktuTU9HeUFhbkFoZWY0RURvMjJWTmZQaWhVUEgiLCJtYWMiOiJmZDlkMGJkYTkyY2M2M2Y0NWY0NGM4ZjAzN2EwNmQ4OTlmODBmN2RlOTM5MWI3MzRlMGI0MGI1OTE3NGUwNWQzIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:23:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235952473\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1082592617 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/profeature/userstory/29</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082592617\", {\"maxDepth\":0})</script>\n"}}