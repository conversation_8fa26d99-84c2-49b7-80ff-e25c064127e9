{"__meta": {"id": "Xbf3babab41f43797984399bd26af3680", "datetime": "2025-06-22 12:26:44", "utime": 1750566404.437317, "method": "GET", "uri": "/tasks/88/comment/29?created_by=ivlyn&sort_date=desc", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[12:26:44] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1750566404.240029, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750566403.929784, "end": 1750566404.437344, "duration": 0.5075600147247314, "duration_str": "508ms", "measures": [{"label": "Booting", "start": 1750566403.929784, "relative_start": 0, "end": 1750566404.220981, "relative_end": 1750566404.220981, "duration": 0.2911968231201172, "duration_str": "291ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1750566404.220992, "relative_start": 0.29120802879333496, "end": 1750566404.437347, "relative_end": 2.86102294921875e-06, "duration": 0.2163548469543457, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24992512, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 18, "templates": [{"name": "tasks.viewCommentList (\\resources\\views\\tasks\\viewCommentList.blade.php)", "param_count": 6, "params": ["title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "creator", "loop", "comment"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "creator", "loop", "comment"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "creator", "loop", "comment"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "creator", "loop", "comment"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "creator", "loop", "comment"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "creator", "loop", "comment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}]}, "route": {"uri": "GET tasks/{task_id}/comment/{sprint_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@viewCommentList", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "tasks.viewCommentList", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=249\">\\app\\Http\\Controllers\\TaskController.php:249-306</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01201, "accumulated_duration_str": "12.01ms", "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0036, "duration_str": "3.6ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "kanban", "start_percent": 0, "width_percent": 29.975}, {"sql": "select * from `tasks` where `tasks`.`id` = '88' limit 1", "type": "query", "params": [], "bindings": ["88"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Task.php", "line": 43}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 258}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Task.php:43", "connection": "kanban", "start_percent": 29.975, "width_percent": 2.581}, {"sql": "select * from `sprint` where `sprint_id` = 29 limit 1", "type": "query", "params": [], "bindings": ["29"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Sprint.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 264}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00025, "duration_str": "250μs", "stmt_id": "\\app\\Sprint.php:41", "connection": "kanban", "start_percent": 32.556, "width_percent": 2.082}, {"sql": "select `created_by` from `taskComment` where `task_id` = '88'", "type": "query", "params": [], "bindings": ["88"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\taskComment.php", "line": 39}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 272}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\taskComment.php:39", "connection": "kanban", "start_percent": 34.638, "width_percent": 3.747}, {"sql": "select count(*) as aggregate from `taskComment` where `task_id` = '88' and `created_by` = 'ivlyn'", "type": "query", "params": [], "bindings": ["88", "ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\taskComment.php", "line": 89}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 282}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00688, "duration_str": "6.88ms", "stmt_id": "\\app\\taskComment.php:89", "connection": "kanban", "start_percent": 38.385, "width_percent": 57.286}, {"sql": "select * from `taskComment` where `task_id` = '88' and `created_by` = 'ivlyn' order by `updated_at` desc limit 5 offset 0", "type": "query", "params": [], "bindings": ["88", "ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\taskComment.php", "line": 89}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 282}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\taskComment.php:89", "connection": "kanban", "start_percent": 95.67, "width_percent": 4.33}]}, "models": {"data": {"App\\taskComment": 1, "App\\Sprint": 1, "App\\Task": 1, "App\\User": 1}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/tasks/88/comment/29?created_by=ivlyn&sort_date=desc\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/tasks/88/comment/29", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-888283143 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>created_by</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ivlyn</span>\"\n  \"<span class=sf-dump-key>sort_date</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888283143\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-506835349 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>created_by</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ivlyn</span>\"\n  \"<span class=sf-dump-key>sort_date</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-506835349\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2091585643 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/tasks/88/comment/29</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkVuK2k0MWpUK2ZxeHVWaFIyYlpGYmc9PSIsInZhbHVlIjoiWDJOam8wRTFrTENrVGZpVHFoTWYrNzRBTXpEQnJ6cG55MEtsdGloR2hZemNkYnFxYUtZbzdoWlFkV1lxa05FL0ZnTGpLZi8rMnBGY0UydDA2NFVSbW1VT1NyYXkra2lBL2IxWFlxS09tMlFPd05RTWR0bk9rbGEvVW0veXZlYXQiLCJtYWMiOiJiMGY1ODNkYjY1ZTVlZGM0YzI2NjgyNWVkODJjNTE4MDZhMzU5ZWExODY2M2E1MTJkZjY5ZTIxMmFmNzQ4Y2Q4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik96MnVONXZ3VFZMR3FmZXlKc0k1WEE9PSIsInZhbHVlIjoiTEI4djYzYW4vZ0oyemN3MUdnVys3Y21VRnlZWHFJdjN4SnBaZTVkQkFha3U4Y1Bhei9VNWtrZGhtQXZtSS9EQ1cxU3FrMUwycjJhOEwyOUNPN2lFK0NQMVZOQUtKc1V4QXlKaEFCVmpNaXNid0hFd0RaOUl2bkhFcjFEeDJ6VlAiLCJtYWMiOiI4OGNhMzZhM2Y3ODQzODhkMGQxY2RkMzMxZjZmNjEyMjkyNmY5OWI3OTI4MTcxYzJiMjZiOWIxMjE3MDNiNmJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091585643\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1822735069 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54706</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"52 characters\">/tasks/88/comment/29?created_by=ivlyn&amp;sort_date=desc</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/tasks/88/comment/29</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/index.php/tasks/88/comment/29</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"31 characters\">created_by=ivlyn&amp;sort_date=desc</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/tasks/88/comment/29</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkVuK2k0MWpUK2ZxeHVWaFIyYlpGYmc9PSIsInZhbHVlIjoiWDJOam8wRTFrTENrVGZpVHFoTWYrNzRBTXpEQnJ6cG55MEtsdGloR2hZemNkYnFxYUtZbzdoWlFkV1lxa05FL0ZnTGpLZi8rMnBGY0UydDA2NFVSbW1VT1NyYXkra2lBL2IxWFlxS09tMlFPd05RTWR0bk9rbGEvVW0veXZlYXQiLCJtYWMiOiJiMGY1ODNkYjY1ZTVlZGM0YzI2NjgyNWVkODJjNTE4MDZhMzU5ZWExODY2M2E1MTJkZjY5ZTIxMmFmNzQ4Y2Q4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik96MnVONXZ3VFZMR3FmZXlKc0k1WEE9PSIsInZhbHVlIjoiTEI4djYzYW4vZ0oyemN3MUdnVys3Y21VRnlZWHFJdjN4SnBaZTVkQkFha3U4Y1Bhei9VNWtrZGhtQXZtSS9EQ1cxU3FrMUwycjJhOEwyOUNPN2lFK0NQMVZOQUtKc1V4QXlKaEFCVmpNaXNid0hFd0RaOUl2bkhFcjFEeDJ6VlAiLCJtYWMiOiI4OGNhMzZhM2Y3ODQzODhkMGQxY2RkMzMxZjZmNjEyMjkyNmY5OWI3OTI4MTcxYzJiMjZiOWIxMjE3MDNiNmJmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1750566403.9298</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1750566403</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822735069\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-12079408 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BNgxUsf2gIxtawp1Y1q9JWoBx2mGAvACnHN2kCr1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12079408\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1633131773 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 04:26:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlJwdFJxZFVSR2o1aXNUbzlqaE1MMmc9PSIsInZhbHVlIjoiWmxuUnpWTkZnVmVZLzlJVk1zUU1YQlpVR00rQmRSelZ3UWMzakVvSHJZTml2MHNtaUFIa0xrL2NyVkFUVzRjcVdzRCtMS0ZCR21XUjJNZnNBWUNoL2F5ZW5mSUE4Q3dqSHlWU2JsRDZVSHR1MTB5OWdrYnJKUXE4ZkJISG1DNTkiLCJtYWMiOiJmMzMzYjZiY2ZhNDg1OTcyNTY0NDYzMDcxZmQwMWM0MzQ5YjNmNWI0MjA0M2FlMDFmMDJiOWQ5N2IzNzYzOGI5IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:26:44 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjA1eCtKNEZWdmkwZ25FYlpZVkJaMHc9PSIsInZhbHVlIjoia2d4eHlodHZYQ2dhWWdsVkZTdnlBZFhXbjJsdEgzRy9GR1c5ZkdITlJFamN0T3l2WEVhM082UzBFTmMyVWJQcjJNQ2tjOFFibGwyMjR1WENvZ2dHR0VTa1U3RXd6YThpNForbW5yWlNOYW5JZkFRaVdONUs2cnZBdlpLVytSTlMiLCJtYWMiOiJkY2E5MzczMTA0OGM0NTE1ODA0NTUwYjdmN2RkNjU4ZGZlYTZhZDYxODcyNjNhYjhiOGY2YmFlNTE4MDY4ZjFiIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:26:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlJwdFJxZFVSR2o1aXNUbzlqaE1MMmc9PSIsInZhbHVlIjoiWmxuUnpWTkZnVmVZLzlJVk1zUU1YQlpVR00rQmRSelZ3UWMzakVvSHJZTml2MHNtaUFIa0xrL2NyVkFUVzRjcVdzRCtMS0ZCR21XUjJNZnNBWUNoL2F5ZW5mSUE4Q3dqSHlWU2JsRDZVSHR1MTB5OWdrYnJKUXE4ZkJISG1DNTkiLCJtYWMiOiJmMzMzYjZiY2ZhNDg1OTcyNTY0NDYzMDcxZmQwMWM0MzQ5YjNmNWI0MjA0M2FlMDFmMDJiOWQ5N2IzNzYzOGI5IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:26:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjA1eCtKNEZWdmkwZ25FYlpZVkJaMHc9PSIsInZhbHVlIjoia2d4eHlodHZYQ2dhWWdsVkZTdnlBZFhXbjJsdEgzRy9GR1c5ZkdITlJFamN0T3l2WEVhM082UzBFTmMyVWJQcjJNQ2tjOFFibGwyMjR1WENvZ2dHR0VTa1U3RXd6YThpNForbW5yWlNOYW5JZkFRaVdONUs2cnZBdlpLVytSTlMiLCJtYWMiOiJkY2E5MzczMTA0OGM0NTE1ODA0NTUwYjdmN2RkNjU4ZGZlYTZhZDYxODcyNjNhYjhiOGY2YmFlNTE4MDY4ZjFiIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 06:26:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1633131773\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-279438378 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Us8EXudZbn6q4hmpgZjZprciUuFZ1ychwqONQg13</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://127.0.0.1:8000/tasks/88/comment/29?created_by=ivlyn&amp;sort_date=desc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-279438378\", {\"maxDepth\":0})</script>\n"}}